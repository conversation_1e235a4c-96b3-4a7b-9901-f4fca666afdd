#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow 简单密码解密工具
"""

import os
import sys
import base64


def decrypt_password(encrypted_password):
    """
    解密RAGFlow前端加密的密码
    
    Args:
        encrypted_password (str): 前端RSA加密后的密码
    
    Returns:
        str: 解密后的明文密码
    """
    try:
        from Cryptodome.PublicKey import RSA
        from Cryptodome.Cipher import PKCS1_v1_5 as Cipher_pkcs1_v1_5
    except ImportError:
        try:
            from Crypto.PublicKey import RSA
            from Crypto.Cipher import PKCS1_v1_5 as Cipher_pkcs1_v1_5
        except ImportError:
            print("错误: 请安装加密库")
            print("运行: pip install pycryptodome")
            sys.exit(1)
    
    # 私钥文件路径
    private_key_path = "conf/private.pem"
    
    if not os.path.exists(private_key_path):
        print(f"错误: 私钥文件不存在: {private_key_path}")
        print("请确保脚本在RAGFlow项目根目录下运行")
        sys.exit(1)
    
    try:
        # 读取私钥
        with open(private_key_path, 'r') as f:
            private_key_content = f.read()
        
        # 导入RSA私钥 (密码是 "Welcome")
        rsa_key = RSA.importKey(private_key_content, "Welcome")
        cipher = Cipher_pkcs1_v1_5.new(rsa_key)
        
        # RSA解密
        decrypted_base64 = cipher.decrypt(
            base64.b64decode(encrypted_password), 
            "解密失败"
        ).decode('utf-8')
        
        # Base64解码得到原始密码
        original_password = base64.b64decode(decrypted_base64).decode('utf-8')
        
        return original_password
        
    except Exception as e:
        print(f"解密失败: {e}")
        return None


def main():
    if len(sys.argv) > 1:
        # 命令行参数
        encrypted_password = sys.argv[1]
        result = decrypt_password(encrypted_password)
        if result:
            print(f"解密结果: {result}")
    else:
        # 交互式
        print("RAGFlow 密码解密工具")
        print("-" * 30)
        
        while True:
            encrypted_password = input("请输入加密密码 (输入q退出): ").strip()
            
            if encrypted_password.lower() == 'q':
                break
                
            if not encrypted_password:
                continue
                
            result = decrypt_password(encrypted_password)
            if result:
                print(f"解密结果: {result}")
            print()


if __name__ == "__main__":
    main()
