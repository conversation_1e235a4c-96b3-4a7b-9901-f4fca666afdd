#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试无认证的 /v1/conversation/ask API

使用方法:
python test_ask_api_no_auth.py
"""

import requests
import json
import sys


def test_ask_api_no_auth(base_url="http://localhost:9380", question="Hello", kb_ids=None, tenant_id=None):
    """
    测试无认证的ask API
    
    Args:
        base_url (str): RAGFlow服务器地址
        question (str): 要问的问题
        kb_ids (list): 知识库ID列表
        tenant_id (str): 租户ID（可选）
    """
    
    # 默认知识库ID（需要根据实际情况修改）
    if kb_ids is None:
        kb_ids = ["your_kb_id_here"]  # 请替换为实际的知识库ID
    
    # API端点
    url = f"{base_url}/v1/conversation/ask"
    
    # 请求数据
    data = {
        "question": question,
        "kb_ids": kb_ids
    }
    
    # 如果提供了租户ID，添加到请求中
    if tenant_id:
        data["tenant_id"] = tenant_id
    
    # 请求头（不包含认证信息）
    headers = {
        "Content-Type": "application/json"
    }
    
    print(f"测试无认证访问 {url}")
    print(f"请求数据: {json.dumps(data, indent=2)}")
    print("-" * 50)
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, stream=True)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code == 200:
            print("✓ API调用成功！无需认证即可访问")
            print("响应内容:")
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data:'):
                        try:
                            data_str = line_str[5:]  # 去掉 'data:' 前缀
                            data_json = json.loads(data_str)
                            print(f"  {json.dumps(data_json, ensure_ascii=False, indent=2)}")
                        except json.JSONDecodeError:
                            print(f"  {line_str}")
                    else:
                        print(f"  {line_str}")
        else:
            print(f"✗ API调用失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"✗ 连接失败，请确认RAGFlow服务器运行在 {base_url}")
    except Exception as e:
        print(f"✗ 请求异常: {e}")


def test_with_auth(base_url="http://localhost:8888", question="Hello", kb_ids=None, token=None):
    """
    测试带认证的ask API（用于对比）
    
    Args:
        base_url (str): RAGFlow服务器地址
        question (str): 要问的问题
        kb_ids (list): 知识库ID列表
        token (str): 认证token
    """
    
    if kb_ids is None:
        kb_ids = ["your_kb_id_here"]
    
    url = f"{base_url}/v1/conversation/ask"
    
    data = {
        "question": question,
        "kb_ids": kb_ids
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    print(f"\n测试带认证访问 {url}")
    print(f"请求数据: {json.dumps(data, indent=2)}")
    print(f"认证头: {headers.get('Authorization', '无')}")
    print("-" * 50)
    
    try:
        response = requests.post(url, json=data, headers=headers, stream=True)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ 带认证的API调用成功")
        else:
            print(f"✗ 带认证的API调用失败: {response.text}")
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")


def get_available_kb_ids(base_url="http://localhost:9380"):
    """
    尝试获取可用的知识库ID（需要认证）
    """
    print("尝试获取可用的知识库ID...")
    
    # 这个API通常需要认证，这里只是示例
    url = f"{base_url}/v1/kb/list"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0 and data.get("data"):
                kb_ids = [kb["id"] for kb in data["data"]]
                print(f"找到知识库ID: {kb_ids}")
                return kb_ids
    except Exception as e:
        print(f"获取知识库ID失败: {e}")
    
    return None


def main():
    print("RAGFlow Ask API 无认证测试工具")
    print("=" * 60)
    
    # 配置参数
    base_url = "http://localhost:9380"  # 修改为你的RAGFlow服务器地址
    question = "什么是人工智能？"
    
    # 尝试获取知识库ID
    kb_ids = get_available_kb_ids(base_url)
    
    if not kb_ids:
        print("\n⚠ 警告: 无法自动获取知识库ID")
        print("请手动设置kb_ids参数")
        print("你可以通过以下方式获取知识库ID:")
        print("1. 登录RAGFlow Web界面查看知识库")
        print("2. 使用API获取知识库列表")
        print("3. 查看数据库中的knowledgebase表")
        
        # 使用示例ID进行测试
        kb_ids = ["example_kb_id"]
        print(f"\n使用示例ID进行测试: {kb_ids}")
    
    # 测试无认证访问
    print("\n" + "=" * 60)
    print("1. 测试无认证访问")
    test_ask_api_no_auth(base_url, question, kb_ids)
    
    # 测试带认证访问（用于对比）
    print("\n" + "=" * 60)
    print("2. 测试带认证访问（对比）")
    test_with_auth(base_url, question, kb_ids)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("\n使用说明:")
    print("1. 确保RAGFlow服务器正在运行")
    print("2. 修改脚本中的base_url为实际服务器地址")
    print("3. 设置正确的kb_ids（知识库ID）")
    print("4. 如果需要测试特定租户，可以在请求中添加tenant_id")


if __name__ == "__main__":
    main()
