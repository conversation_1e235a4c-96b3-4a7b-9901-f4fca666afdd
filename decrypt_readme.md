# RAGFlow 密码解密工具使用说明

## 概述

这些脚本用于解密RAGFlow前端RSA加密的密码，将加密后的密码还原为明文。

## 文件说明

1. **`decrypt_password.py`** - 完整功能的解密工具
2. **`simple_decrypt.py`** - 简化版解密工具
3. **`decrypt_readme.md`** - 本说明文件

## 环境要求

### Python版本
- Python 3.6+

### 依赖库
```bash
pip install pycryptodome
```

或者使用旧版本：
```bash
pip install pycrypto
```

## 使用方法

### 方法1: 使用完整版工具 (decrypt_password.py)

#### 1. 命令行单个密码解密
```bash
python decrypt_password.py "加密后的密码字符串"
```

#### 2. 交互式解密
```bash
python decrypt_password.py
```
然后按提示输入加密密码

#### 3. 批量解密文件
```bash
python decrypt_password.py -f passwords.txt
```

#### 4. 指定私钥文件路径
```bash
python decrypt_password.py -k /path/to/private.pem "加密密码"
```

#### 5. 指定解密方法
```bash
python decrypt_password.py -m v1 "加密密码"  # 使用方法1
python decrypt_password.py -m v2 "加密密码"  # 使用方法2
python decrypt_password.py -m auto "加密密码"  # 自动选择(默认)
```

### 方法2: 使用简化版工具 (simple_decrypt.py)

#### 1. 命令行解密
```bash
python simple_decrypt.py "加密后的密码字符串"
```

#### 2. 交互式解密
```bash
python simple_decrypt.py
```

## 示例

假设您有一个加密后的密码：`abcdef123456...`

```bash
# 使用完整版工具
python decrypt_password.py "abcdef123456..."

# 使用简化版工具
python simple_decrypt.py "abcdef123456..."
```

输出示例：
```
✓ 使用方法1解密成功
解密结果: mypassword123
```

## 批量解密

创建一个文本文件 `passwords.txt`，每行一个加密密码：
```
encrypted_password_1
encrypted_password_2
encrypted_password_3
```

然后运行：
```bash
python decrypt_password.py -f passwords.txt
```

## 文件结构要求

脚本会自动查找私钥文件，支持以下路径：
- `conf/private.pem` (相对于当前目录)
- `./conf/private.pem`
- `../conf/private.pem` (上级目录)

确保私钥文件 `private.pem` 存在于 `conf` 目录中。

## 加密原理

RAGFlow前端密码加密流程：
1. 原始密码 → Base64编码
2. Base64编码结果 → RSA公钥加密
3. RSA加密结果 → Base64编码 (最终传输的密文)

解密流程：
1. 传输密文 → Base64解码
2. Base64解码结果 → RSA私钥解密
3. RSA解密结果 → Base64解码 (得到原始密码)

## 安全注意事项

1. **私钥保护**: 私钥文件包含敏感信息，请妥善保管
2. **密码保护**: 私钥文件使用密码 "Welcome" 保护
3. **权限控制**: 建议设置适当的文件权限
4. **日志安全**: 避免在日志中记录明文密码

## 故障排除

### 1. 找不到私钥文件
```
错误: 私钥文件不存在: conf/private.pem
```
**解决方案**: 确保在RAGFlow项目根目录下运行脚本，或使用 `-k` 参数指定私钥路径

### 2. 缺少加密库
```
ImportError: 请安装 pycryptodome 或 pycrypto
```
**解决方案**: 
```bash
pip install pycryptodome
```

### 3. 解密失败
```
解密失败: Fail to decrypt password!
```
**可能原因**:
- 加密密码格式错误
- 私钥文件损坏
- 加密算法版本不匹配

**解决方案**: 尝试使用不同的解密方法 (`-m v1` 或 `-m v2`)

### 4. 私钥密码错误
```
ValueError: RSA key format is not supported
```
**解决方案**: 确认私钥密码是 "Welcome"

## 技术支持

如果遇到问题，请检查：
1. Python版本是否符合要求
2. 依赖库是否正确安装
3. 私钥文件是否存在且可读
4. 加密密码格式是否正确

## 版本历史

- v1.0: 初始版本，支持基本解密功能
- v1.1: 添加批量解密和多种解密方法
- v1.2: 改进错误处理和用户体验
