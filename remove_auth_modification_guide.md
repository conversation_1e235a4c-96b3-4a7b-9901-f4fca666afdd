# RAGFlow Ask API 去除认证修改指南

## 概述

本文档详细说明了如何去除 RAGFlow 中 `/v1/conversation/ask` API 的认证要求，使其可以无需任何认证（包括 token 和 cookie）即可访问。

## 修改内容

### 1. 主要修改文件

**文件**: `api/apps/conversation_app.py`

**修改位置**: 第312-330行的 `ask_about` 函数

### 2. 具体修改

#### 原始代码
```python
@manager.route("/ask", methods=["POST"])  # noqa: F821
@login_required
@validate_request("question", "kb_ids")
def ask_about():
    req = request.json
    uid = current_user.id
```

#### 修改后代码
```python
@manager.route("/ask", methods=["POST"])  # noqa: F821
# @login_required  # 已移除认证要求，允许无认证访问
@validate_request("question", "kb_ids")
def ask_about():
    req = request.json
    # 获取租户ID：优先使用请求中的tenant_id，否则使用第一个知识库的租户ID
    tenant_id = req.get("tenant_id")
    if not tenant_id:
        # 从知识库中获取租户ID
        from api.db.services.knowledgebase_service import KnowledgebaseService
        kb_ids = req["kb_ids"]
        if kb_ids:
            e, kb = KnowledgebaseService.get_by_id(kb_ids[0])
            if e:
                tenant_id = kb.tenant_id
            else:
                return get_data_error_result(message="Knowledgebase not found!")
        else:
            return get_data_error_result(message="No tenant_id provided and no valid kb_ids found!")
```

#### 函数调用修改
```python
# 原始代码
for ans in ask(req["question"], req["kb_ids"], uid):

# 修改后代码  
for ans in ask(req["question"], req["kb_ids"], tenant_id):
```

## 修改说明

### 1. 移除认证装饰器
- 注释掉 `@login_required` 装饰器
- 保留 `@validate_request("question", "kb_ids")` 进行基本参数验证

### 2. 租户ID获取策略
修改后的API支持以下几种方式获取租户ID：

1. **显式提供**: 在请求JSON中包含 `tenant_id` 字段
2. **自动推断**: 从提供的知识库ID中获取对应的租户ID
3. **错误处理**: 如果无法获取有效的租户ID，返回错误信息

### 3. 兼容性考虑
- 保持原有的请求参数格式不变
- 添加可选的 `tenant_id` 参数
- 保持响应格式不变

## API 使用方法

### 1. 基本请求格式

```bash
curl -X POST http://localhost:9380/v1/conversation/ask \
  -H "Content-Type: application/json" \
  -d '{
    "question": "什么是人工智能？",
    "kb_ids": ["your_knowledge_base_id"]
  }'
```

### 2. 指定租户ID的请求

```bash
curl -X POST http://localhost:9380/v1/conversation/ask \
  -H "Content-Type: application/json" \
  -d '{
    "question": "什么是人工智能？",
    "kb_ids": ["your_knowledge_base_id"],
    "tenant_id": "your_tenant_id"
  }'
```

### 3. Python 示例

```python
import requests
import json

url = "http://localhost:9380/v1/conversation/ask"
data = {
    "question": "什么是人工智能？",
    "kb_ids": ["your_knowledge_base_id"]
}

response = requests.post(url, json=data, stream=True)

for line in response.iter_lines():
    if line and line.startswith(b'data:'):
        data_str = line.decode('utf-8')[5:]  # 去掉 'data:' 前缀
        try:
            result = json.loads(data_str)
            print(result)
        except json.JSONDecodeError:
            pass
```

## 安全考虑

### 1. 风险评估
- **数据访问**: 移除认证后，任何人都可以访问指定知识库的内容
- **资源消耗**: 无认证访问可能导致资源滥用
- **审计追踪**: 无法追踪具体的用户操作

### 2. 建议的安全措施

#### 网络层面
```nginx
# Nginx 配置示例：限制访问来源
location /v1/conversation/ask {
    allow ***********/24;  # 只允许内网访问
    allow 10.0.0.0/8;      # 允许特定网段
    deny all;              # 拒绝其他所有访问
    
    proxy_pass http://ragflow_backend;
}
```

#### 应用层面
```python
# 可以添加简单的API密钥验证
def ask_about():
    req = request.json
    
    # 可选：添加简单的API密钥验证
    api_key = request.headers.get("X-API-Key")
    if api_key != "your_secret_api_key":
        return get_data_error_result(message="Invalid API key!")
    
    # ... 其余代码
```

#### 速率限制
```python
# 使用 Flask-Limiter 添加速率限制
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["100 per hour"]
)

@manager.route("/ask", methods=["POST"])
@limiter.limit("10 per minute")  # 每分钟最多10次请求
def ask_about():
    # ... 代码
```

## 测试验证

### 1. 使用提供的测试脚本
```bash
python test_ask_api_no_auth.py
```

### 2. 手动测试步骤

1. **获取知识库ID**
   - 登录RAGFlow Web界面
   - 查看知识库列表，获取知识库ID

2. **测试无认证访问**
   ```bash
   curl -X POST http://localhost:9380/v1/conversation/ask \
     -H "Content-Type: application/json" \
     -d '{"question": "测试问题", "kb_ids": ["实际的知识库ID"]}'
   ```

3. **验证响应**
   - 检查是否返回200状态码
   - 验证响应内容是否正确
   - 确认无需认证头即可访问

## 回滚方案

如果需要恢复认证要求，只需要：

1. **恢复装饰器**
   ```python
   @manager.route("/ask", methods=["POST"])
   @login_required  # 取消注释
   @validate_request("question", "kb_ids")
   ```

2. **恢复用户ID获取**
   ```python
   def ask_about():
       req = request.json
       uid = current_user.id  # 恢复原始代码
   ```

3. **恢复函数调用**
   ```python
   for ans in ask(req["question"], req["kb_ids"], uid):  # 使用uid而不是tenant_id
   ```

## 常见问题

### Q1: 修改后API返回"Knowledgebase not found!"
**A**: 检查提供的知识库ID是否正确，确保知识库存在且状态正常。

### Q2: 如何获取知识库ID？
**A**: 
- 通过Web界面查看
- 使用 `/v1/kb/list` API（需要认证）
- 直接查询数据库的 `knowledgebase` 表

### Q3: 是否影响其他API？
**A**: 不会，此修改只影响 `/v1/conversation/ask` 端点，其他API的认证要求保持不变。

### Q4: 如何限制访问权限？
**A**: 可以通过网络层（如Nginx）、应用层API密钥或速率限制等方式控制访问。

## 总结

通过以上修改，`/v1/conversation/ask` API 现在可以无需任何认证即可访问。请根据实际安全需求考虑是否需要添加其他形式的访问控制。
