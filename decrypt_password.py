#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow 密码解密工具
用于解密前端RSA加密的密码

使用方法:
1. 命令行参数: python decrypt_password.py <encrypted_password>
2. 交互式输入: python decrypt_password.py
3. 批量解密文件: python decrypt_password.py -f <file_path>

作者: AI Assistant
日期: 2025-01-27
"""

import os
import sys
import base64
import argparse
from pathlib import Path


def get_private_key_path():
    """获取私钥文件路径"""
    # 当前脚本所在目录
    script_dir = Path(__file__).parent
    
    # 尝试多个可能的私钥路径
    possible_paths = [
        script_dir / "conf" / "private.pem",  # 相对于脚本目录
        Path("conf/private.pem"),  # 相对于当前工作目录
        Path("./conf/private.pem"),  # 当前目录下的conf
        script_dir.parent / "conf" / "private.pem",  # 上级目录的conf
    ]
    
    for path in possible_paths:
        if path.exists():
            return str(path)
    
    # 如果都找不到，返回默认路径
    return str(script_dir / "conf" / "private.pem")


def decrypt_password_v1(encrypted_password, private_key_path=None):
    """
    解密密码 - 方法1 (对应原始的decrypt函数)
    
    Args:
        encrypted_password (str): 加密后的密码
        private_key_path (str): 私钥文件路径，如果为None则自动查找
    
    Returns:
        str: 解密后的明文密码
    """
    try:
        from Cryptodome.PublicKey import RSA
        from Cryptodome.Cipher import PKCS1_v1_5 as Cipher_pkcs1_v1_5
    except ImportError:
        try:
            from Crypto.PublicKey import RSA
            from Crypto.Cipher import PKCS1_v1_5 as Cipher_pkcs1_v1_5
        except ImportError:
            raise ImportError("请安装 pycryptodome 或 pycrypto: pip install pycryptodome")
    
    if private_key_path is None:
        private_key_path = get_private_key_path()
    
    if not os.path.exists(private_key_path):
        raise FileNotFoundError(f"私钥文件不存在: {private_key_path}")
    
    # 读取私钥文件
    with open(private_key_path, 'r') as f:
        private_key_content = f.read()
    
    # 导入RSA私钥 (密码是 "Welcome")
    rsa_key = RSA.importKey(private_key_content, "Welcome")
    cipher = Cipher_pkcs1_v1_5.new(rsa_key)
    
    # 解密
    decrypted_base64 = cipher.decrypt(
        base64.b64decode(encrypted_password), 
        "Fail to decrypt password!"
    ).decode('utf-8')
    
    # Base64解码得到原始密码
    original_password = base64.b64decode(decrypted_base64).decode('utf-8')
    
    return original_password


def decrypt_password_v2(encrypted_password, private_key_path=None):
    """
    解密密码 - 方法2 (对应decrypt2函数，处理特殊情况)
    
    Args:
        encrypted_password (str): 加密后的密码
        private_key_path (str): 私钥文件路径，如果为None则自动查找
    
    Returns:
        str: 解密后的明文密码
    """
    try:
        from Crypto.Cipher import PKCS1_v1_5 as Cipher_PKCS1_v1_5
        from Crypto.PublicKey import RSA
    except ImportError:
        try:
            from Cryptodome.Cipher import PKCS1_v1_5 as Cipher_PKCS1_v1_5
            from Cryptodome.PublicKey import RSA
        except ImportError:
            raise ImportError("请安装 pycryptodome 或 pycrypto: pip install pycryptodome")
    
    from base64 import b64decode, b16decode
    
    if private_key_path is None:
        private_key_path = get_private_key_path()
    
    if not os.path.exists(private_key_path):
        raise FileNotFoundError(f"私钥文件不存在: {private_key_path}")
    
    # 读取私钥文件
    with open(private_key_path, 'r') as f:
        pem = f.read()
    
    # Base64解码
    decode_data = b64decode(encrypted_password)
    
    # 处理特殊情况：如果解码后的数据长度是127，需要补零
    if len(decode_data) == 127:
        hex_fixed = '00' + decode_data.hex()
        decode_data = b16decode(hex_fixed.upper())
    
    # 导入RSA私钥
    rsa_key = RSA.importKey(pem, "Welcome")
    cipher = Cipher_PKCS1_v1_5.new(rsa_key)
    
    # 解密
    decrypt_text = cipher.decrypt(decode_data, None)
    
    # Base64解码得到原始密码
    original_password = b64decode(decrypt_text).decode()
    
    return original_password


def decrypt_password(encrypted_password, private_key_path=None, method='auto'):
    """
    智能解密密码，自动尝试不同的解密方法
    
    Args:
        encrypted_password (str): 加密后的密码
        private_key_path (str): 私钥文件路径
        method (str): 解密方法 ('auto', 'v1', 'v2')
    
    Returns:
        str: 解密后的明文密码
    """
    if method == 'v1':
        return decrypt_password_v1(encrypted_password, private_key_path)
    elif method == 'v2':
        return decrypt_password_v2(encrypted_password, private_key_path)
    else:  # auto
        # 先尝试方法1
        try:
            result = decrypt_password_v1(encrypted_password, private_key_path)
            print(f"✓ 使用方法1解密成功")
            return result
        except Exception as e1:
            print(f"⚠ 方法1解密失败: {e1}")
            
            # 再尝试方法2
            try:
                result = decrypt_password_v2(encrypted_password, private_key_path)
                print(f"✓ 使用方法2解密成功")
                return result
            except Exception as e2:
                print(f"✗ 方法2解密失败: {e2}")
                raise Exception(f"所有解密方法都失败了。方法1错误: {e1}, 方法2错误: {e2}")


def decrypt_from_file(file_path, private_key_path=None):
    """
    从文件中批量解密密码
    
    Args:
        file_path (str): 包含加密密码的文件路径（每行一个）
        private_key_path (str): 私钥文件路径
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    results = []
    for i, line in enumerate(lines, 1):
        encrypted_password = line.strip()
        if not encrypted_password:
            continue
            
        try:
            decrypted = decrypt_password(encrypted_password, private_key_path)
            results.append(f"第{i}行: {decrypted}")
            print(f"第{i}行解密成功: {decrypted}")
        except Exception as e:
            results.append(f"第{i}行解密失败: {e}")
            print(f"第{i}行解密失败: {e}")
    
    return results


def main():
    parser = argparse.ArgumentParser(description='RAGFlow 密码解密工具')
    parser.add_argument('encrypted_password', nargs='?', help='要解密的密码')
    parser.add_argument('-f', '--file', help='包含加密密码的文件路径')
    parser.add_argument('-k', '--key', help='私钥文件路径')
    parser.add_argument('-m', '--method', choices=['auto', 'v1', 'v2'], 
                       default='auto', help='解密方法')
    
    args = parser.parse_args()
    
    try:
        if args.file:
            # 批量解密文件
            print(f"从文件批量解密: {args.file}")
            decrypt_from_file(args.file, args.key)
        elif args.encrypted_password:
            # 命令行参数解密
            result = decrypt_password(args.encrypted_password, args.key, args.method)
            print(f"\n解密结果: {result}")
        else:
            # 交互式输入
            print("RAGFlow 密码解密工具")
            print("=" * 50)
            
            # 检查私钥文件
            private_key_path = args.key or get_private_key_path()
            if os.path.exists(private_key_path):
                print(f"✓ 找到私钥文件: {private_key_path}")
            else:
                print(f"✗ 私钥文件不存在: {private_key_path}")
                return
            
            while True:
                encrypted_password = input("\n请输入要解密的密码 (输入 'quit' 退出): ").strip()
                
                if encrypted_password.lower() in ['quit', 'exit', 'q']:
                    print("再见!")
                    break
                
                if not encrypted_password:
                    print("请输入有效的加密密码")
                    continue
                
                try:
                    result = decrypt_password(encrypted_password, private_key_path, args.method)
                    print(f"✓ 解密成功: {result}")
                except Exception as e:
                    print(f"✗ 解密失败: {e}")
    
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
